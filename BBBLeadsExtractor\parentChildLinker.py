import json
import re
from typing import Dict, Any, List, Tuple

class ParentChildLinker:
    def __init__(self):
        self.unmatched_id = "UNMATCHED_DSO"
    def process_extracted_data(self, extracted_results: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        dsos = []
        branches = []
        independent_practices = []

        for result in extracted_results:
            if result['type'] == 'DSO_HQ':
                dsos.append(result['data'])
            elif result['type'] == 'DSO_BRANCH':
                branches.append(result['data'])
            elif result['type'] == 'DDS':
                independent_practices.append(result['data'])

        matched_branches, unmatched_branches = self._match_branches_by_business_id(branches, dsos)
        all_practices = independent_practices + matched_branches + unmatched_branches

        total_dsos = len(dsos)
        total_branches = len(branches)
        matched_count = len(matched_branches)
        unmatched_count = len(unmatched_branches)
        independent_count = len(independent_practices)

        print(f"📊 Processing Summary:")
        print(f"   DSO Headquarters: {total_dsos}")
        print(f"   Total Branches: {total_branches}")
        print(f"   ✅ Successfully Matched: {matched_count}")
        print(f"   ❌ Unmatched Branches: {unmatched_count}")
        print(f"   🏢 Independent Practices: {independent_count}")
        print(f"   📈 Match Success Rate: {(matched_count/total_branches*100):.1f}%" if total_branches > 0 else "   📈 Match Success Rate: N/A")

        return dsos, all_practices
    def _extract_business_id_from_html(self, html_content: str) -> str:
        patterns = [
            r'"business_id"\s*:\s*"([^"]+)"',
            r'business_id["\']?\s*:\s*["\']?([^"\']+)["\']?',
            r'businessId["\']?\s*:\s*["\']?([^"\']+)["\']?'
        ]

        for pattern in patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return ""
    def _match_branches_by_business_id(self, branches: List[Dict[str, Any]], dsos: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        matched = []
        unmatched = []

        dso_business_ids = {}
        for dso in dsos:
            html_content = dso.get('html_content', '')
            if html_content:
                business_id = self._extract_business_id_from_html(html_content)
                if business_id:
                    dso_business_ids[business_id] = dso['id']
                    print(f"🏢 DSO '{dso.get('name', 'Unknown')}' has business_id: {business_id}")

        for branch in branches:
            html_content = branch.get('html_content', '')
            branch_business_id = ""
            branch_name = branch.get('name', 'Unknown')

            if html_content:
                branch_business_id = self._extract_business_id_from_html(html_content)

            if branch_business_id and branch_business_id in dso_business_ids:
                branch['dso_id'] = dso_business_ids[branch_business_id]
                matched.append(branch)
                print(f"✅ Matched branch '{branch_name}' (ID: {branch_business_id}) to DSO")
            else:
                branch['dso_id'] = self.unmatched_id
                unmatched.append(branch)
                print(f"❌ No match for branch '{branch_name}' (ID: {branch_business_id or 'Not found'})")

        return matched, unmatched


    def save_results(self, dsos: List[Dict[str, Any]], practices: List[Dict[str, Any]]):
        with open("dsos.json", 'w', encoding='utf-8') as f:
            json.dump(dsos, f, indent=4, ensure_ascii=False)
        with open("dentalpractices.json", 'w', encoding='utf-8') as f:
            json.dump(practices, f, indent=4, ensure_ascii=False)

        matched_branches = sum(1 for p in practices if p.get('dso_id') and p['dso_id'] != self.unmatched_id and p['dso_id'] is not None)
        unmatched_branches = sum(1 for p in practices if p.get('dso_id') == self.unmatched_id)
        independent_practices = sum(1 for p in practices if p.get('dso_id') is None)
        dsos_with_no_branches = len([dso for dso in dsos if not any(p.get('dso_id') == dso['id'] for p in practices)])

        total_branches = matched_branches + unmatched_branches
        match_rate = (matched_branches / total_branches * 100) if total_branches > 0 else 0

        print(f"\n📊 COMPREHENSIVE FINAL REPORT:")
        print(f"   🏢 DSO Headquarters: {len(dsos)}")
        print(f"   🌿 Total Branches Found: {total_branches}")
        print(f"   ✅ Successfully Matched Branches: {matched_branches}")
        print(f"   ❌ Unmatched Branches: {unmatched_branches}")
        print(f"   🏥 Independent Practices: {independent_practices}")
        print(f"   🚫 DSOs with No Branches: {dsos_with_no_branches}")
        print(f"   📈 Branch Match Success Rate: {match_rate:.1f}%")
        print(f"   📁 Total Records: {len(practices)} practices + {len(dsos)} DSOs")
        print(f"   💾 Files saved: dsos.json, dentalpractices.json")

        if unmatched_branches > 0:
            print(f"\n⚠️  WARNING: {unmatched_branches} branches could not be matched to their parent DSO")
            print(f"   This indicates potential issues with business_id extraction or data quality")

        with open("processing_report.txt", "w") as f:
            f.write(f"DSO Headquarters: {len(dsos)}\n")
            f.write(f"Total Branches: {total_branches}\n")
            f.write(f"Successfully Matched Branches: {matched_branches}\n")
            f.write(f"Unmatched Branches: {unmatched_branches}\n")
            f.write(f"Independent Practices: {independent_practices}\n")
            f.write(f"DSOs with No Branches: {dsos_with_no_branches}\n")
            f.write(f"Branch Match Success Rate: {match_rate:.1f}%\n")
            f.write(f"Total Records: {len(practices)} practices + {len(dsos)} DSOs\n")
        print("📄 Detailed report saved to processing_report.txt")