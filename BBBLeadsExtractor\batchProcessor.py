import os
import sys
import time
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from unifiedExtractor import UnifiedBBBExtractor
from parentChildLinker import ParentChildLinker

class BatchProcessor:
    def __init__(self, threshold: float = 0.8, max_workers: int = None):
        self.threshold = threshold
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.results_lock = Lock()
        self.results = []
        self.errors = []

    def process_file(self, html_file: str) -> dict:
        extractor = UnifiedBBBExtractor()
        try:
            result = extractor.process_html_file(html_file)
            return {'success': True, 'data': result, 'file': os.path.basename(html_file)}
        except Exception as e:
            return {'success': False, 'error': str(e), 'file': os.path.basename(html_file)}

    def process_directory(self, directory_path: str) -> tuple:
        if not os.path.exists(directory_path):
            raise ValueError(f"Directory not found: {directory_path}")

        html_files = []
        for root, _, files in os.walk(directory_path):
            html_files.extend(os.path.join(root, f) for f in files if f.lower().endswith('.html'))

        if not html_files:
            raise ValueError("No HTML files found")

        print(f"🔍 Processing {len(html_files)} files with {self.max_workers} threads")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {executor.submit(self.process_file, f): f for f in html_files}

            for future in as_completed(future_to_file):
                result = future.result()

                with self.results_lock:
                    if result['success']:
                        self.results.append(result['data'])
                        print(f"✅ {result['data']['type']}: {result['file']}")
                    else:
                        self.errors.append(result)
                        print(f"❌ {result['file']}: {result['error']}")

        if self.errors:
            print(f"⚠️  {len(self.errors)} files failed to process")

        linker = ParentChildLinker(self.threshold)
        dsos, practices = linker.process_extracted_data(self.results)
        linker.save_results(dsos, practices)

        return dsos, practices

def main():
    parser = argparse.ArgumentParser(description="Process BBB HTML files with parallel processing")
    parser.add_argument('input_directory', help='Directory with HTML files')
    parser.add_argument('--threshold', '-t', type=float, default=0.8, help='Matching threshold (default: 0.8)')
    parser.add_argument('--workers', '-w', type=int, help='Number of worker threads (default: auto)')
    parser.add_argument('--output-dir', '-o', default='.', help='Output directory')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    args = parser.parse_args()

    if not os.path.exists(args.input_directory):
        print(f"❌ Directory not found: {args.input_directory}")
        sys.exit(1)

    if not (0.0 <= args.threshold <= 1.0):
        print(f"❌ Invalid threshold: {args.threshold}")
        sys.exit(1)

    os.makedirs(args.output_dir, exist_ok=True)
    original_cwd = os.getcwd()
    os.chdir(args.output_dir)
    
    start_time = time.time()
    try:
        processor = BatchProcessor(args.threshold, args.workers)
        print(f"🚀 Processing {args.input_directory} (threshold: {args.threshold})")
        print("="*60)

        dsos, practices = processor.process_directory(os.path.abspath(args.input_directory))

        print("="*60)
        print(f"✅ Complete! DSOs: {len(dsos)}, Practices: {len(practices)}")

    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    finally:
        os.chdir(original_cwd)
        end_time = time.time()
        print(f"\nTotal execution time: {end_time - start_time:.2f} seconds.")

if __name__ == "__main__":
    main()